export type ExcelRow = Record<string, string | number | null>;

export type ProcessedRow = {
	originalIndex: number;
	code?: string;
	description?: string;
	quantity?: number;
	uom?: string;
	rate?: number;
	subtotal?: number;
	factor?: number;
	hasYellowFill?: boolean;
};

export type ClassifiedRow = ProcessedRow & {
	classification: 'detail' | 'summary' | 'category' | 'ignore';
	categoryPrefix?: string;
	finalDescription?: string;
	appliedFactor?: number;
	manualCategories?: string[]; // For category rows: manually selected categories to prepend
};

export type ColumnMapping = {
	code?: number;
	description?: number;
	quantity?: number;
	uom?: number;
	rate?: number;
	subtotal?: number;
	factor?: number;
};

/**
 * First RegExp to match a header wins (case‑insensitive).
 * List patterns most‑specific → least‑specific.
 */
export const COLUMN_PATTERNS: Record<keyof ColumnMapping, RegExp[]> = {
	code: [/^code$/i, /^wbs\.?code$/i, /\bwbs\b/i, /\bcost\s*code\b/i],
	description: [/^description$/i, /^desc$/i, /\b(scope|item\s*name)\b/i],
	quantity: [/^quantity$/i, /^qty$/i, /\bq(?:uantity)?ty?\b/i],
	uom: [/^uom$/i, /^unit(?:\s*of\s*measure)?$/i, /^units?$/i],
	rate: [/^rate$/i, /\b(material)?\s*rate\b/i, /^unit\s*rate$/i],
	subtotal: [/^sub\s*total$/i, /^subtotal$/i, /\bsub[-\s]*total\b/i],
	factor: [/^factor$/i, /\bmultiplier\b/i, /\badjust(?:ment)?\s*factor\b/i],
};

/**
 * Automatically map column headers to their types using regex patterns
 */
export function matchColumns(headers: string[]): ColumnMapping {
	const mapping: ColumnMapping = {};

	headers.forEach((header, index) => {
		if (!header || typeof header !== 'string') return;

		const cleanHeader = header.trim();
		if (!cleanHeader) return;

		// Check each column type
		for (const [columnType, patterns] of Object.entries(COLUMN_PATTERNS)) {
			// Skip if we already found a match for this column type
			if (mapping[columnType as keyof ColumnMapping] !== undefined) continue;

			// Check if any pattern matches
			const matches = patterns.some((pattern) => pattern.test(cleanHeader));
			if (matches) {
				mapping[columnType as keyof ColumnMapping] = index;
				break; // Stop checking other patterns for this header
			}
		}
	});

	return mapping;
}

/**
 * Parse WBS code into hierarchical components
 */
export function parseWbsCode(code: string): {
	level: number;
	in_level_code: string;
	parent_code: string | null;
} {
	if (!code || typeof code !== 'string') {
		throw new Error('Invalid WBS code');
	}

	const parts = code.split('.');
	const level = parts.length;
	const in_level_code = parts[parts.length - 1];
	const parent_code = level > 1 ? parts.slice(0, -1).join('.') : null;

	return {
		level,
		in_level_code,
		parent_code,
	};
}

/**
 * Build category prefix from category stack
 */
export function buildCategoryPrefix(categoryStack: string[]): string {
	if (categoryStack.length === 0) return '';

	return categoryStack.map((category) => `[${category.trim()}]`).join('');
}

/**
 * Classify a row based on its content
 */
export function classifyRow(row: ProcessedRow): ClassifiedRow['classification'] {
	// Detail row - has a code
	if (row.code && row.code.trim()) {
		return 'detail';
	}

	// Summary row - description starts with "TOTAL"
	if (row.description && row.description.trim().toUpperCase().startsWith('TOTAL ')) {
		return 'summary';
	}

	// Category row - has description but no code, usually surrounded by empty rows
	if (row.description && row.description.trim() && !row.code) {
		return 'category';
	}

	// Everything else is ignored (headers, empty rows, etc.)
	return 'ignore';
}

/**
 * Collect isolated categories (categories with blank rows above AND below)
 */
export function collectIsolatedCategories(rows: ProcessedRow[]): string[] {
	const isolatedCategories: string[] = [];

	for (let i = 0; i < rows.length; i++) {
		const row = rows[i];
		const classification = classifyRow(row);

		if (classification === 'category') {
			// Check if this category has blank rows both above and below
			const hasBlankAbove = i > 0 && isBlankRow(rows[i - 1]);
			const hasBlankBelow = i < rows.length - 1 && isBlankRow(rows[i + 1]);

			if (hasBlankAbove && hasBlankBelow) {
				const categoryName = row.description?.trim() || '';
				if (categoryName && !isolatedCategories.includes(categoryName)) {
					isolatedCategories.push(categoryName);
				}
			}
		}
	}

	return isolatedCategories;
}

/**
 * Check if a row is blank (ignore classification with no description)
 */
function isBlankRow(row: ProcessedRow): boolean {
	const classification = classifyRow(row);
	return classification === 'ignore' && !row.description?.trim();
}

/**
 * Apply simplified category logic with manual category selections
 */
export function applyCategoriesToRows(
	rows: ProcessedRow[],
	categoryEdits: Record<number, string> = {},
	manualCategorySelections: Record<number, string[]> = {},
): ClassifiedRow[] {
	const result: ClassifiedRow[] = [];
	let currentCategoryPrefix = '';

	for (let i = 0; i < rows.length; i++) {
		const row = rows[i];
		const classification = classifyRow(row);

		const classifiedRow: ClassifiedRow = {
			...row,
			classification,
		};

		if (classification === 'category') {
			// Use edited category name if available, otherwise original description
			const categoryName = categoryEdits[i] || row.description || '';

			// Get manual category selections for this row
			const manualCategories = manualCategorySelections[i] || [];
			if (manualCategories.length > 0) {
				classifiedRow.manualCategories = manualCategories;
			}

			// Build final description with manual categories prepended
			const manualPrefix = manualCategories.map((cat) => `[${cat.trim()}]`).join('');
			classifiedRow.finalDescription = manualPrefix + categoryName;

			// Check if this category should be automatically applied (immediately followed by detail rows)
			const nextRowIndex = i + 1;
			if (nextRowIndex < rows.length && classifyRow(rows[nextRowIndex]) === 'detail') {
				// Automatically apply this category to subsequent detail rows
				// Include both manual categories and the category itself in the prefix
				const fullPrefix = manualPrefix + `[${categoryName.trim()}]`;
				currentCategoryPrefix = fullPrefix;
			} else {
				// This category is not automatically applied (has blank rows after it)
				currentCategoryPrefix = '';
			}
		} else if (classification === 'detail') {
			// Apply automatic category prefix if available
			if (currentCategoryPrefix) {
				classifiedRow.categoryPrefix = currentCategoryPrefix;
				classifiedRow.finalDescription = currentCategoryPrefix + (row.description || '');
			} else {
				classifiedRow.finalDescription = row.description || '';
			}
		} else if (classification === 'ignore' && !row.description?.trim()) {
			// Blank row - reset automatic category prefix
			currentCategoryPrefix = '';
		}

		result.push(classifiedRow);
	}

	return result;
}

/**
 * Validate that required columns are mapped
 */
export function validateColumnMapping(mapping: ColumnMapping): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];
	const requiredColumns: (keyof ColumnMapping)[] = ['code', 'description', 'quantity', 'rate'];

	// Check required columns
	for (const column of requiredColumns) {
		if (mapping[column] === undefined) {
			errors.push(`${column} column is required`);
		}
	}

	// Check for duplicate mappings
	const usedIndices = new Set<number>();
	for (const [_column, index] of Object.entries(mapping)) {
		if (index !== undefined) {
			if (usedIndices.has(index)) {
				errors.push(`Column index ${index} is mapped to multiple fields`);
			}
			usedIndices.add(index);
		}
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}

/**
 * Transform classified rows into import data format
 */
export function transformToImportData(
	classifiedRows: ClassifiedRow[],
	projectId: string,
): { project_id: string; items: any[] } {
	const detailRows = classifiedRows.filter((row) => row.classification === 'detail');

	const items = detailRows.map((row) => ({
		code: row.code || '',
		description: row.finalDescription || row.description || '',
		quantity: row.quantity || 0,
		unit: row.uom || '',
		material_rate: row.rate || 0,
		factor: row.appliedFactor || row.factor || undefined,
		labor_rate: undefined,
		productivity_per_hour: undefined,
		remarks: undefined,
	}));

	return {
		project_id: projectId,
		items,
	};
}
