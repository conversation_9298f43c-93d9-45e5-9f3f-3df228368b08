export type ExcelRow = Record<string, string | number | null>;

export type ProcessedRow = {
	originalIndex: number;
	code?: string;
	description?: string;
	quantity?: number;
	uom?: string;
	rate?: number;
	subtotal?: number;
	factor?: number;
	hasYellowFill?: boolean;
};

export type ClassifiedRow = ProcessedRow & {
	classification: 'detail' | 'summary' | 'category' | 'ignore';
	categoryPrefix?: string;
	finalDescription?: string;
	appliedFactor?: number;
};

export type ColumnMapping = {
	code?: number;
	description?: number;
	quantity?: number;
	uom?: number;
	rate?: number;
	subtotal?: number;
	factor?: number;
};

/**
 * First RegExp to match a header wins (case‑insensitive).
 * List patterns most‑specific → least‑specific.
 */
export const COLUMN_PATTERNS: Record<keyof ColumnMapping, RegExp[]> = {
	code: [/^code$/i, /^wbs\.?code$/i, /\bwbs\b/i, /\bcost\s*code\b/i],
	description: [/^description$/i, /^desc$/i, /\b(scope|item\s*name)\b/i],
	quantity: [/^quantity$/i, /^qty$/i, /\bq(?:uantity)?ty?\b/i],
	uom: [/^uom$/i, /^unit(?:\s*of\s*measure)?$/i, /^units?$/i],
	rate: [/^rate$/i, /\b(material)?\s*rate\b/i, /^unit\s*rate$/i],
	subtotal: [/^sub\s*total$/i, /^subtotal$/i, /\bsub[-\s]*total\b/i],
	factor: [/^factor$/i, /\bmultiplier\b/i, /\badjust(?:ment)?\s*factor\b/i],
};

/**
 * Automatically map column headers to their types using regex patterns
 */
export function matchColumns(headers: string[]): ColumnMapping {
	const mapping: ColumnMapping = {};

	headers.forEach((header, index) => {
		if (!header || typeof header !== 'string') return;

		const cleanHeader = header.trim();
		if (!cleanHeader) return;

		// Check each column type
		for (const [columnType, patterns] of Object.entries(COLUMN_PATTERNS)) {
			// Skip if we already found a match for this column type
			if (mapping[columnType as keyof ColumnMapping] !== undefined) continue;

			// Check if any pattern matches
			const matches = patterns.some((pattern) => pattern.test(cleanHeader));
			if (matches) {
				mapping[columnType as keyof ColumnMapping] = index;
				break; // Stop checking other patterns for this header
			}
		}
	});

	return mapping;
}

/**
 * Parse WBS code into hierarchical components
 */
export function parseWbsCode(code: string): {
	level: number;
	in_level_code: string;
	parent_code: string | null;
} {
	if (!code || typeof code !== 'string') {
		throw new Error('Invalid WBS code');
	}

	const parts = code.split('.');
	const level = parts.length;
	const in_level_code = parts[parts.length - 1];
	const parent_code = level > 1 ? parts.slice(0, -1).join('.') : null;

	return {
		level,
		in_level_code,
		parent_code,
	};
}

/**
 * Build category prefix from category stack
 */
export function buildCategoryPrefix(categoryStack: string[]): string {
	if (categoryStack.length === 0) return '';

	return categoryStack.map((category) => `[${category.trim()}]`).join('');
}

/**
 * Classify a row based on its content
 */
export function classifyRow(row: ProcessedRow): ClassifiedRow['classification'] {
	// Detail row - has a code
	if (row.code && row.code.trim()) {
		return 'detail';
	}

	// Summary row - description starts with "TOTAL"
	if (row.description && row.description.trim().toUpperCase().startsWith('TOTAL ')) {
		return 'summary';
	}

	// Category row - has description but no code, usually surrounded by empty rows
	if (row.description && row.description.trim() && !row.code) {
		return 'category';
	}

	// Everything else is ignored (headers, empty rows, etc.)
	return 'ignore';
}

/**
 * Apply category prefixes to detail rows with corrected stack management
 */
export function applyCategoriesToRows(
	rows: ProcessedRow[],
	categoryEdits: Record<number, string> = {},
): ClassifiedRow[] {
	const categoryStack: string[] = [];
	const result: ClassifiedRow[] = [];

	// Pre-process to identify which categories should be dropped
	const categoriesToDrop = identifyCategoriesToDrop(rows);

	for (let i = 0; i < rows.length; i++) {
		const row = rows[i];
		const classification = classifyRow(row);

		const classifiedRow: ClassifiedRow = {
			...row,
			classification,
		};

		if (classification === 'category') {
			// Use edited category name if available, otherwise original description
			const categoryName = categoryEdits[i] || row.description || '';

			// Push the new category onto the stack
			categoryStack.push(categoryName.trim());

			classifiedRow.finalDescription = categoryName;
		} else if (classification === 'detail') {
			// Apply category prefix to detail rows
			const prefix = buildCategoryPrefix(categoryStack);
			classifiedRow.categoryPrefix = prefix;
			classifiedRow.finalDescription = prefix + (row.description || '');
		} else if (classification === 'ignore' && !row.description?.trim()) {
			// Handle empty/blank rows - drop categories that should be dropped
			if (categoriesToDrop.has(i) && categoryStack.length > 0) {
				categoryStack.pop();
			}
		}

		result.push(classifiedRow);
	}

	return result;
}

/**
 * Identify which blank row indices should trigger category drops
 */
function identifyCategoriesToDrop(rows: ProcessedRow[]): Set<number> {
	const dropIndices = new Set<number>();

	for (let i = 0; i < rows.length; i++) {
		const row = rows[i];
		const classification = classifyRow(row);

		// Only process blank rows
		if (classification !== 'ignore' || row.description?.trim()) {
			continue;
		}

		// Look backward to find the most recent category
		const categoryInfo = findPreviousCategory(i, rows);
		if (!categoryInfo) continue;

		const { categoryIndex, hasDetailsBetween } = categoryInfo;

		// Rule 1: If category was followed by detail rows, drop it immediately
		if (hasDetailsBetween) {
			dropIndices.add(i);
			continue;
		}

		// Rule 2 & 3: Category was followed by blank rows
		// Check if we should drop based on future patterns
		const shouldDrop = shouldDropCategoryWithBlankRows(i, categoryIndex, rows);
		if (shouldDrop) {
			dropIndices.add(i);
		}
	}

	return dropIndices;
}

/**
 * Find the most recent category before the given index
 */
function findPreviousCategory(
	blankRowIndex: number,
	rows: ProcessedRow[],
): { categoryIndex: number; hasDetailsBetween: boolean } | null {
	let hasDetailsBetween = false;

	for (let i = blankRowIndex - 1; i >= 0; i--) {
		const classification = classifyRow(rows[i]);

		if (classification === 'detail') {
			hasDetailsBetween = true;
		} else if (classification === 'category') {
			return { categoryIndex: i, hasDetailsBetween };
		}
	}

	return null;
}

/**
 * Determine if a category with blank rows should be dropped
 */
function shouldDropCategoryWithBlankRows(
	blankRowIndex: number,
	categoryIndex: number,
	rows: ProcessedRow[],
): boolean {
	// Count blank rows after the category
	const blankRowsAfterCategory = countBlankRowsAfterCategory(categoryIndex, rows);

	// Look ahead to see what comes next
	const nextNonBlankIndex = findNextNonBlankRow(blankRowIndex + 1, rows);
	if (nextNonBlankIndex === -1) return false;

	const nextRow = rows[nextNonBlankIndex];
	const nextClassification = classifyRow(nextRow);

	if (nextClassification === 'category') {
		// Check if the new category also has blank rows after it
		const blankRowsAfterNext = countBlankRowsAfterCategory(nextNonBlankIndex, rows);
		return blankRowsAfterNext > 0;
	} else if (nextClassification === 'detail') {
		// Only drop if this blank row comes AFTER the detail section
		// Check if we're currently in a blank row that comes after details
		const detailStartIndex = nextNonBlankIndex;
		const detailEndIndex = findEndOfDetailSection(detailStartIndex, rows);

		// If the current blank row is before the detail section, don't drop yet
		if (blankRowIndex < detailStartIndex) {
			return false;
		}

		// If we're after the detail section, check for equal blank rows
		const blankRowsAfterDetails = countBlankRowsAfter(detailEndIndex + 1, rows);
		return blankRowsAfterDetails >= blankRowsAfterCategory;
	}

	return false;
}

/**
 * Count blank rows immediately after a category
 */
function countBlankRowsAfterCategory(categoryIndex: number, rows: ProcessedRow[]): number {
	let count = 0;
	for (let i = categoryIndex + 1; i < rows.length; i++) {
		const classification = classifyRow(rows[i]);
		if (classification === 'ignore' && !rows[i].description?.trim()) {
			count++;
		} else {
			break;
		}
	}
	return count;
}

/**
 * Find the end of a detail section starting from the given index
 */
function findEndOfDetailSection(startIndex: number, rows: ProcessedRow[]): number {
	let lastDetailIndex = startIndex;
	for (let i = startIndex; i < rows.length; i++) {
		const classification = classifyRow(rows[i]);
		if (classification === 'detail') {
			lastDetailIndex = i;
		} else {
			break;
		}
	}
	return lastDetailIndex;
}

/**
 * Find the next non-blank row index
 */
function findNextNonBlankRow(startIndex: number, rows: ProcessedRow[]): number {
	for (let i = startIndex; i < rows.length; i++) {
		const classification = classifyRow(rows[i]);
		if (classification !== 'ignore' || rows[i].description?.trim()) {
			return i;
		}
	}
	return -1;
}

/**
 * Count blank rows starting from index
 */
function countBlankRowsAfter(startIndex: number, rows: ProcessedRow[]): number {
	let count = 0;
	for (let i = startIndex; i < rows.length; i++) {
		const classification = classifyRow(rows[i]);
		if (classification === 'ignore' && !rows[i].description?.trim()) {
			count++;
		} else {
			break;
		}
	}
	return count;
}

/**
 * Validate that required columns are mapped
 */
export function validateColumnMapping(mapping: ColumnMapping): {
	isValid: boolean;
	errors: string[];
} {
	const errors: string[] = [];
	const requiredColumns: (keyof ColumnMapping)[] = ['code', 'description', 'quantity', 'rate'];

	// Check required columns
	for (const column of requiredColumns) {
		if (mapping[column] === undefined) {
			errors.push(`${column} column is required`);
		}
	}

	// Check for duplicate mappings
	const usedIndices = new Set<number>();
	for (const [_column, index] of Object.entries(mapping)) {
		if (index !== undefined) {
			if (usedIndices.has(index)) {
				errors.push(`Column index ${index} is mapped to multiple fields`);
			}
			usedIndices.add(index);
		}
	}

	return {
		isValid: errors.length === 0,
		errors,
	};
}

/**
 * Transform classified rows into import data format
 */
export function transformToImportData(
	classifiedRows: ClassifiedRow[],
	projectId: string,
): { project_id: string; items: any[] } {
	const detailRows = classifiedRows.filter((row) => row.classification === 'detail');

	const items = detailRows.map((row) => ({
		code: row.code || '',
		description: row.finalDescription || row.description || '',
		quantity: row.quantity || 0,
		unit: row.uom || '',
		material_rate: row.rate || 0,
		factor: row.appliedFactor || row.factor || undefined,
		labor_rate: undefined,
		productivity_per_hour: undefined,
		remarks: undefined,
	}));

	return {
		project_id: projectId,
		items,
	};
}
