<script lang="ts">
	import { Button } from '$lib/components/ui/button';
	import { Alert, AlertDescription } from '$lib/components/ui/alert';
	import {
		displaySize,
		FileDropZone,
		MEGABYTE,
		type FileDropZoneProps,
	} from '$lib/components/ui/file-drop-zone';
	import {
		Table,
		TableBody,
		TableCell,
		TableHead,
		TableHeader,
		TableRow,
	} from '$lib/components/ui/table';
	import { X, AlertTriangle, Info } from '@lucide/svelte';
	import { toast } from 'svelte-sonner';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { parseExcelFile, getExcelPreviewData } from '$lib/excel_parser';
	import type { ColumnMapping, ProcessedRow, ClassifiedRow } from '$lib/budget_import_utils';
	import ColumnClassifier from '$lib/components/budget-import/ColumnClassifier.svelte';
	import RowClassifier from '$lib/components/budget-import/RowClassifier.svelte';
	import ImportSummary from '$lib/components/budget-import/ImportSummary.svelte';

	const { data } = $props();

	// Wizard state
	let currentStep = $state(parseInt(page.url.searchParams.get('step') || '1'));
	let uploadedFile = $state<File | null>(null);
	let fileBuffer = $state<ArrayBuffer | null>(null);
	let parseResult = $state<any>(null);
	let previewData = $state<any[]>([]);
	let columnMapping = $state<ColumnMapping>({});
	let classifiedRows = $state<ClassifiedRow[]>([]);
	let isImporting = $state(false);

	// Load state from localStorage on mount
	// $effect(() => {
	// 	const saved = localStorage.getItem('budget_import_state');
	// 	if (saved) {
	// 		try {
	// 			const state = JSON.parse(saved);
	// 			if (state.step) currentStep = state.step;
	// 			// Note: File buffer can't be saved to localStorage due to size
	// 		} catch (e) {
	// 			console.warn('Failed to load import state:', e);
	// 		}
	// 	}
	// });

	// // Save state to localStorage when it changes
	// $effect(() => {
	// 	const state = {
	// 		step: currentStep,
	// 		columnMapping,
	// 		// Don't save file buffer or large data structures
	// 	};
	// 	localStorage.setItem('budget_import_state', JSON.stringify(state));
	// });

	$effect(()=>{
		if ([2,3,4].includes(currentStep) && !uploadedFile) {
			currentStep = 1;
			updateUrl();
		}
	})

	// Update URL when step changes
	function updateUrl() {
		const url = new URL(page.url);
		url.searchParams.set('step', currentStep.toString());
		goto(url.toString(), { replaceState: true, noScroll: true });
	}

	const steps = [
		{ number: 1, title: 'Upload File', description: 'Select Excel file to import' },
		{ number: 2, title: 'Preview Data', description: 'Review raw data from file' },
		{ number: 3, title: 'Map Columns', description: 'Identify column types' },
		{ number: 4, title: 'Classify Rows', description: 'Categorize and organize data' },
		{ number: 5, title: 'Import', description: 'Review and import to database' },
	];

	const onUpload: FileDropZoneProps['onUpload'] = async (uploadedFiles) => {
		if (uploadedFiles.length === 0) return;

		const file = uploadedFiles[0];
		uploadedFile = file;

		try {
			fileBuffer = await file.arrayBuffer();
			parseResult = parseExcelFile(fileBuffer);
			previewData = getExcelPreviewData(fileBuffer, 20);

			toast.success('File uploaded successfully');
			currentStep = 2;

			updateUrl();
		} catch (error) {
			console.error('Failed to parse file:', error);
			toast.error('Failed to parse Excel file', {
				description: error instanceof Error ? error.message : 'Unknown error',
			});
		}
	};

	const onFileRejected: FileDropZoneProps['onFileRejected'] = async ({ reason, file }) => {
		console.log({ reason, file });
		toast.error(`${file.name} failed to upload!`, { description: reason });
	};

	function removeFile() {
		uploadedFile = null;
		fileBuffer = null;
		parseResult = null;
		previewData = [];
		currentStep = 1;
	}

	function nextStep() {
		if (currentStep < steps.length) {
			currentStep += 1;

			updateUrl();
		}
	}

	function prevStep() {
		if (currentStep > 1) {
			currentStep -= 1;

			updateUrl();
		}
	}

	function handleColumnMappingChange(mapping: ColumnMapping) {
		columnMapping = mapping;
	}

	function handleClassifiedRowsChange(rows: ClassifiedRow[]) {
		classifiedRows = rows;
	}

	async function handleImport(importData: any) {
		isImporting = true;

		try {
			const response = await data.supabase.rpc('import_budget_data', {
				p_project_id: data.project.project_id,
				p_items: importData.items,
			});

			if (response.error) {
				throw new Error(response.error.message);
			}

			const result = response.data as {
				inserted_count: number;
				wbs_created_count: number;
				duration_ms: number;
			};
			toast.success('Budget imported successfully!', {
				description: `Imported ${result.inserted_count} items, created ${result.wbs_created_count} WBS items in ${result.duration_ms}ms`,
			});

			// Clear state and redirect
			localStorage.removeItem('budget_import_state');
			goto(
				`/org/${data.project.client.organization.name}/clients/${data.project.client.name}/projects/${data.project.name}/budget`,
			);
		} catch (error) {
			console.error('Import failed:', error);
			toast.error('Import failed', {
				description: error instanceof Error ? error.message : 'Unknown error',
			});
		} finally {
			isImporting = false;
		}
	}
</script>

<svelte:head>
	<title>Import Budget - {data.project.name} - {data.project.client.name}</title>
</svelte:head>

<div class="container mx-auto max-w-6xl py-8">
	<div class="mb-8">
		<h1 class="mb-2 text-3xl font-bold">Import Budget</h1>
		<p class="text-muted-foreground">
			Upload a CostX Excel export to create your budget items with hierarchical categorization.
		</p>
	</div>

	<!-- Progress Steps -->
	<div class="mb-8">
		<div class="flex items-center justify-between">
			{#each steps as step (step.number)}
				<div class="flex items-center">
					<div
						class="flex h-8 w-8 items-center justify-center rounded-full border-2 text-sm font-medium"
						class:bg-primary={currentStep >= step.number}
						class:text-primary-foreground={currentStep >= step.number}
						class:border-primary={currentStep >= step.number}
						class:border-muted={currentStep < step.number}
						class:text-muted-foreground={currentStep < step.number}
					>
						{step.number}
					</div>
					<div class="ml-2 hidden sm:block">
						<div class="text-sm font-medium">{step.title}</div>
						<div class="text-muted-foreground text-xs">{step.description}</div>
					</div>
				</div>
				{#if step.number < steps.length}
					<div
						class="mx-4 h-0.5 flex-1"
						class:bg-primary={currentStep > step.number}
						class:bg-muted={currentStep <= step.number}
					></div>
				{/if}
			{/each}
		</div>
	</div>

	<!-- Step Content -->
	<div class="bg-card rounded-lg border p-6">
		{#if currentStep === 1}
			<!-- Step 1: File Upload -->
			<div class="space-y-6">
				<div>
					<h2 class="mb-2 text-xl font-semibold">Step 1: Upload Excel File</h2>
					<p class="text-muted-foreground">
						Select a CostX Excel export file (.xlsx) to import budget data.
					</p>
				</div>

				<FileDropZone
					{onUpload}
					{onFileRejected}
					maxFileSize={10 * MEGABYTE}
					accept=".xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
					fileCount={uploadedFile ? 1 : 0}
				/>

				{#if uploadedFile}
					<div class="flex items-center justify-between rounded border p-3">
						<div class="flex flex-col">
							<span class="font-medium">{uploadedFile.name}</span>
							<span class="text-muted-foreground text-sm">{displaySize(uploadedFile.size)}</span>
						</div>
						<Button variant="outline" size="icon" onclick={removeFile}>
							<X class="h-4 w-4" />
						</Button>
					</div>
				{/if}
			</div>
		{:else if currentStep === 2}
			<!-- Step 2: Preview Data -->
			<div class="space-y-6">
				<div>
					<h2 class="mb-2 text-xl font-semibold">Step 2: Preview Data</h2>
					<p class="text-muted-foreground">Review the raw data extracted from your Excel file.</p>
				</div>

				{#if parseResult?.hasMultipleSheets}
					<Alert>
						<Info class="h-4 w-4" />
						<AlertDescription>
							Multiple sheets detected. Only the first sheet will be imported.
						</AlertDescription>
					</Alert>
				{/if}

				{#if parseResult?.hasYellowFill}
					<Alert>
						<AlertTriangle class="h-4 w-4" />
						<AlertDescription>
							Yellow highlighted cells detected. Consider exporting at a deeper level for more
							detailed data.
						</AlertDescription>
					</Alert>
				{/if}

				{#if previewData.length > 0}
					<div class="overflow-hidden rounded-lg border">
						<Table>
							<TableHeader>
								<TableRow>
									{#each Object.keys(previewData[0]) as key (key)}
										<TableHead>{key}</TableHead>
									{/each}
								</TableRow>
							</TableHeader>
							<TableBody>
								{#each previewData as row, index (index)}
									<TableRow>
										{#each Object.keys(previewData[0]) as key (key)}
											<TableCell class="max-w-32 truncate">{row[key] || ''}</TableCell>
										{/each}
									</TableRow>
								{/each}
							</TableBody>
						</Table>
					</div>
					<p class="text-muted-foreground text-sm">
						Showing first {previewData.length} rows. Found {parseResult?.rows?.length || 0} total rows.
					</p>
				{/if}

				<div class="flex justify-between">
					<Button variant="outline" onclick={prevStep}>Back</Button>
					<Button onclick={nextStep}>Next: Map Columns</Button>
				</div>
			</div>
		{:else if currentStep === 3}
			<!-- Step 3: Column Mapping -->
			{#if parseResult?.headers}
				<ColumnClassifier
					headers={parseResult.headers}
					sampleRows={parseResult.rows.slice(0, 5)}
					onMappingChange={handleColumnMappingChange}
					onNext={nextStep}
					onBack={prevStep}
				/>
			{/if}
		{:else if currentStep === 4}
			<!-- Step 4: Row Classification -->
			{#if parseResult?.rows}
				<RowClassifier
					rows={parseResult.rows}
					{columnMapping}
					onClassifiedRowsChange={handleClassifiedRowsChange}
					onNext={nextStep}
					onBack={prevStep}
				/>
			{/if}
		{:else if currentStep === 5}
			<!-- Step 5: Import Summary -->
			<ImportSummary
				{classifiedRows}
				projectId={data.project.project_id}
				onImport={handleImport}
				onBack={prevStep}
				{isImporting}
			/>
		{/if}
	</div>
</div>
