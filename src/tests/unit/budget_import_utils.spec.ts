import { describe, it, expect } from 'vitest';
import {
	matchColumns,
	parseWbsCode,
	buildCategoryPrefix,
	classifyRow,
	applyCategoriesToRows,
	collectIsolatedCategories,
	validateColumnMapping,
	transformToImportData,
	type ColumnMapping,
	type ProcessedRow,
} from '$lib/budget_import_utils';

describe('matchColumns', () => {
	it('should match common column headers', () => {
		const headers = ['Code', 'Description', 'Quantity', 'UOM', 'Rate', 'SubTotal'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
		expect(mapping.subtotal).toBe(5);
	});

	it('should handle case-insensitive matching', () => {
		const headers = ['code', 'DESCRIPTION', 'qty', 'Unit', 'material rate'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
	});

	it('should handle WBS code variations', () => {
		const headers = ['WBS Code', 'WBS.Code', 'Cost Code'];
		const mapping1 = matchColumns([headers[0]]);
		const mapping2 = matchColumns([headers[1]]);
		const mapping3 = matchColumns([headers[2]]);

		expect(mapping1.code).toBe(0);
		expect(mapping2.code).toBe(0);
		expect(mapping3.code).toBe(0);
	});

	it('should not map unknown headers', () => {
		const headers = ['Unknown1', 'Unknown2', 'Random'];
		const mapping = matchColumns(headers);

		expect(Object.values(mapping).every((v) => v === undefined)).toBe(true);
	});
});

describe('parseWbsCode', () => {
	it('should parse single level codes', () => {
		const result = parseWbsCode('1');
		expect(result).toEqual({
			level: 1,
			in_level_code: '1',
			parent_code: null,
		});
	});

	it('should parse multi-level codes', () => {
		const result = parseWbsCode('1.2.3');
		expect(result).toEqual({
			level: 3,
			in_level_code: '3',
			parent_code: '1.2',
		});
	});

	it('should handle complex codes', () => {
		const result = parseWbsCode('A.B.C.D.E');
		expect(result).toEqual({
			level: 5,
			in_level_code: 'E',
			parent_code: 'A.B.C.D',
		});
	});

	it('should throw error for invalid codes', () => {
		expect(() => parseWbsCode('')).toThrow('Invalid WBS code');
		expect(() => parseWbsCode(null as any)).toThrow('Invalid WBS code');
	});
});

describe('buildCategoryPrefix', () => {
	it('should build prefix from category stack', () => {
		const stack = ['Site Work', 'Excavation'];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});

	it('should handle empty stack', () => {
		const prefix = buildCategoryPrefix([]);
		expect(prefix).toBe('');
	});

	it('should trim category names', () => {
		const stack = [' Site Work ', ' Excavation '];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});
});

describe('classifyRow', () => {
	it('should classify detail rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			code: '1.1.1',
			description: 'Concrete work',
			quantity: 100,
		};
		expect(classifyRow(row)).toBe('detail');
	});

	it('should classify category rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			description: 'Site Work',
		};
		expect(classifyRow(row)).toBe('category');
	});

	it('should classify summary rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			description: 'TOTAL Site Work',
		};
		expect(classifyRow(row)).toBe('summary');
	});

	it('should classify ignore rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
		};
		expect(classifyRow(row)).toBe('ignore');
	});
});

describe('collectIsolatedCategories', () => {
	it('should collect categories with blank rows above and below', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0 }, // Blank
			{ originalIndex: 1, description: 'Isolated Category A' }, // Isolated
			{ originalIndex: 2 }, // Blank
			{ originalIndex: 3, description: 'Connected Category' }, // Not isolated (no blank above)
			{ originalIndex: 4, code: '1.1', description: 'Detail', quantity: 100 },
			{ originalIndex: 5 }, // Blank
			{ originalIndex: 6, description: 'Isolated Category B' }, // Isolated
			{ originalIndex: 7 }, // Blank
		];

		const result = collectIsolatedCategories(rows);
		expect(result).toEqual(['Isolated Category A', 'Isolated Category B']);
	});

	it('should not collect categories that are not isolated', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Category A' }, // No blank above
			{ originalIndex: 1 }, // Blank
			{ originalIndex: 2, description: 'Category B' }, // No blank below
			{ originalIndex: 3, code: '1.1', description: 'Detail', quantity: 100 },
		];

		const result = collectIsolatedCategories(rows);
		expect(result).toEqual([]);
	});

	it('should maintain order and avoid duplicates', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0 }, // Blank
			{ originalIndex: 1, description: 'Category A' }, // Isolated
			{ originalIndex: 2 }, // Blank
			{ originalIndex: 3 }, // Blank
			{ originalIndex: 4, description: 'Category A' }, // Isolated (duplicate name)
			{ originalIndex: 5 }, // Blank
			{ originalIndex: 6 }, // Blank
			{ originalIndex: 7, description: 'Category B' }, // Isolated
			{ originalIndex: 8 }, // Blank
		];

		const result = collectIsolatedCategories(rows);
		expect(result).toEqual(['Category A', 'Category B']); // No duplicates, maintains order
	});
});

describe('applyCategoriesToRows', () => {
	it('should automatically apply category prefixes when category is immediately followed by detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 2, code: '1.2', description: 'Grading', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work]Excavation');
		expect(result[2].categoryPrefix).toBe('[Site Work]');
		expect(result[2].finalDescription).toBe('[Site Work]Grading');
	});

	it('should NOT automatically apply category prefixes when category has blank rows after it', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1 }, // Blank row
			{ originalIndex: 2, code: '1.1', description: 'Excavation', quantity: 100 },
		];

		const result = applyCategoriesToRows(rows);

		expect(result[0].classification).toBe('category');
		expect(result[2].classification).toBe('detail');
		expect(result[2].categoryPrefix).toBeUndefined();
		expect(result[2].finalDescription).toBe('Excavation');
	});

	it('should handle category edits', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
		];

		const categoryEdits = { 0: 'Modified Site Work' };
		const result = applyCategoriesToRows(rows, categoryEdits);

		expect(result[1].categoryPrefix).toBe('[Modified Site Work]');
		expect(result[1].finalDescription).toBe('[Modified Site Work]Excavation');
	});

	it('should handle manual category selections for category rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Category A' },
			{ originalIndex: 1, code: '1.1', description: 'Detail Item', quantity: 100 },
		];

		const manualCategorySelections = { 0: ['Manual Cat 1', 'Manual Cat 2'] };
		const result = applyCategoriesToRows(rows, {}, manualCategorySelections);

		expect(result[0].manualCategories).toEqual(['Manual Cat 1', 'Manual Cat 2']);
		expect(result[0].finalDescription).toBe('[Manual Cat 1][Manual Cat 2]Category A');
		expect(result[1].categoryPrefix).toBe('[Category A]');
		expect(result[1].finalDescription).toBe('[Category A]Detail Item');
	});

	it('should reset automatic category prefix when encountering blank rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 2 }, // Empty row - should reset automatic prefix
			{ originalIndex: 3, description: 'Concrete Work' },
			{ originalIndex: 4, code: '2.1', description: 'Foundation', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		// First category and detail row
		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work]Excavation');

		// Empty row
		expect(result[2].classification).toBe('ignore');

		// Second category and detail row (should have Concrete Work prefix)
		expect(result[3].classification).toBe('category');
		expect(result[4].classification).toBe('detail');
		expect(result[4].categoryPrefix).toBe('[Concrete Work]');
		expect(result[4].finalDescription).toBe('[Concrete Work]Foundation');
	});

	it('should combine manual categories with automatic category prefixes', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
		];

		const manualCategorySelections = { 0: ['Phase 1', 'Area A'] };
		const result = applyCategoriesToRows(rows, {}, manualCategorySelections);

		// Category row should show manual categories in final description
		expect(result[0].manualCategories).toEqual(['Phase 1', 'Area A']);
		expect(result[0].finalDescription).toBe('[Phase 1][Area A]Site Work');

		// Detail row should get automatic prefix from the category
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work]Excavation');
	});

	it('should handle mixed scenarios with manual and automatic categories', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Phase 1' },
			{ originalIndex: 1 }, // Blank - Phase 1 won't be automatically applied
			{ originalIndex: 2, description: 'Site Work' },
			{ originalIndex: 3, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 4 }, // Blank - reset automatic prefix
			{ originalIndex: 5, description: 'Concrete Work' },
			{ originalIndex: 6 }, // Blank - Concrete Work won't be automatically applied
			{ originalIndex: 7, code: '2.1', description: 'Foundation', quantity: 50 },
		];

		const manualCategorySelections = { 5: ['Phase 1'] }; // Manually add Phase 1 to Concrete Work
		const result = applyCategoriesToRows(rows, {}, manualCategorySelections);

		// Excavation gets automatic Site Work prefix
		expect(result[3].categoryPrefix).toBe('[Site Work]');
		expect(result[3].finalDescription).toBe('[Site Work]Excavation');

		// Concrete Work shows manual category in final description
		expect(result[5].manualCategories).toEqual(['Phase 1']);
		expect(result[5].finalDescription).toBe('[Phase 1]Concrete Work');

		// Foundation has no automatic prefix (blank row after Concrete Work)
		expect(result[7].categoryPrefix).toBeUndefined();
		expect(result[7].finalDescription).toBe('Foundation');
	});
});

describe('validateColumnMapping', () => {
	it('should validate required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			quantity: 2,
			rate: 3,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(true);
		expect(result.errors).toEqual([]);
	});

	it('should detect missing required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			// missing quantity and rate
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('quantity column is required');
		expect(result.errors).toContain('rate column is required');
	});

	it('should detect duplicate mappings', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 0, // duplicate index
			quantity: 1,
			rate: 2,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('Column index 0 is mapped to multiple fields');
	});
});

describe('transformToImportData', () => {
	it('should transform classified rows to import format', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				classification: 'category' as const,
				description: 'Site Work',
				finalDescription: 'Site Work',
			},
			{
				originalIndex: 1,
				classification: 'detail' as const,
				code: '1.1',
				description: 'Excavation',
				finalDescription: '[Site Work]Excavation',
				quantity: 100,
				uom: 'm3',
				rate: 50,
				factor: 1.1,
			},
			{
				originalIndex: 2,
				classification: 'ignore' as const,
			},
		];

		const result = transformToImportData(classifiedRows, 'project-123');

		expect(result.project_id).toBe('project-123');
		expect(result.items).toHaveLength(1);
		expect(result.items[0]).toEqual({
			code: '1.1',
			description: '[Site Work]Excavation',
			quantity: 100,
			unit: 'm3',
			material_rate: 50,
			factor: 1.1,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			remarks: undefined,
		});
	});

	it('should handle missing values gracefully', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				classification: 'detail' as const,
				code: '1.1',
				description: 'Test item',
				finalDescription: 'Test item',
				// missing quantity, rate, etc.
			},
		];

		const result = transformToImportData(classifiedRows, 'project-123');

		expect(result.items[0]).toEqual({
			code: '1.1',
			description: 'Test item',
			quantity: 0,
			unit: '',
			material_rate: 0,
			factor: undefined,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			remarks: undefined,
		});
	});
});
