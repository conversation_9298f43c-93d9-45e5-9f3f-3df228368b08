import { describe, it, expect } from 'vitest';
import {
	matchColumns,
	parseWbsCode,
	buildCategoryPrefix,
	classifyRow,
	applyCategoriesToRows,
	validateColumnMapping,
	transformToImportData,
	type ColumnMapping,
	type ProcessedRow,
} from '$lib/budget_import_utils';

describe('matchColumns', () => {
	it('should match common column headers', () => {
		const headers = ['Code', 'Description', 'Quantity', 'UOM', 'Rate', 'SubTotal'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
		expect(mapping.subtotal).toBe(5);
	});

	it('should handle case-insensitive matching', () => {
		const headers = ['code', 'DESCRIPTION', 'qty', 'Unit', 'material rate'];
		const mapping = matchColumns(headers);

		expect(mapping.code).toBe(0);
		expect(mapping.description).toBe(1);
		expect(mapping.quantity).toBe(2);
		expect(mapping.uom).toBe(3);
		expect(mapping.rate).toBe(4);
	});

	it('should handle WBS code variations', () => {
		const headers = ['WBS Code', 'WBS.Code', 'Cost Code'];
		const mapping1 = matchColumns([headers[0]]);
		const mapping2 = matchColumns([headers[1]]);
		const mapping3 = matchColumns([headers[2]]);

		expect(mapping1.code).toBe(0);
		expect(mapping2.code).toBe(0);
		expect(mapping3.code).toBe(0);
	});

	it('should not map unknown headers', () => {
		const headers = ['Unknown1', 'Unknown2', 'Random'];
		const mapping = matchColumns(headers);

		expect(Object.values(mapping).every((v) => v === undefined)).toBe(true);
	});
});

describe('parseWbsCode', () => {
	it('should parse single level codes', () => {
		const result = parseWbsCode('1');
		expect(result).toEqual({
			level: 1,
			in_level_code: '1',
			parent_code: null,
		});
	});

	it('should parse multi-level codes', () => {
		const result = parseWbsCode('1.2.3');
		expect(result).toEqual({
			level: 3,
			in_level_code: '3',
			parent_code: '1.2',
		});
	});

	it('should handle complex codes', () => {
		const result = parseWbsCode('A.B.C.D.E');
		expect(result).toEqual({
			level: 5,
			in_level_code: 'E',
			parent_code: 'A.B.C.D',
		});
	});

	it('should throw error for invalid codes', () => {
		expect(() => parseWbsCode('')).toThrow('Invalid WBS code');
		expect(() => parseWbsCode(null as any)).toThrow('Invalid WBS code');
	});
});

describe('buildCategoryPrefix', () => {
	it('should build prefix from category stack', () => {
		const stack = ['Site Work', 'Excavation'];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});

	it('should handle empty stack', () => {
		const prefix = buildCategoryPrefix([]);
		expect(prefix).toBe('');
	});

	it('should trim category names', () => {
		const stack = [' Site Work ', ' Excavation '];
		const prefix = buildCategoryPrefix(stack);
		expect(prefix).toBe('[Site Work][Excavation]');
	});
});

describe('classifyRow', () => {
	it('should classify detail rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			code: '1.1.1',
			description: 'Concrete work',
			quantity: 100,
		};
		expect(classifyRow(row)).toBe('detail');
	});

	it('should classify category rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			description: 'Site Work',
		};
		expect(classifyRow(row)).toBe('category');
	});

	it('should classify summary rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
			description: 'TOTAL Site Work',
		};
		expect(classifyRow(row)).toBe('summary');
	});

	it('should classify ignore rows', () => {
		const row: ProcessedRow = {
			originalIndex: 1,
		};
		expect(classifyRow(row)).toBe('ignore');
	});
});

describe('applyCategoriesToRows', () => {
	it('should apply category prefixes to detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 2, description: 'Concrete Work' },
			{ originalIndex: 3, code: '2.1', description: 'Foundation', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work]Excavation');

		expect(result[2].classification).toBe('category');
		expect(result[3].classification).toBe('detail');
		// Without an empty row between categories, they stack up
		expect(result[3].categoryPrefix).toBe('[Site Work][Concrete Work]');
		expect(result[3].finalDescription).toBe('[Site Work][Concrete Work]Foundation');
	});

	it('should handle category edits', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
		];

		const categoryEdits = { 0: 'Modified Site Work' };
		const result = applyCategoriesToRows(rows, categoryEdits);

		expect(result[1].categoryPrefix).toBe('[Modified Site Work]');
		expect(result[1].finalDescription).toBe('[Modified Site Work]Excavation');
	});

	it('should pop categories when encountering empty rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Site Work' },
			{ originalIndex: 1, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 2 }, // Empty row - should pop 'Site Work'
			{ originalIndex: 3, description: 'Concrete Work' },
			{ originalIndex: 4, code: '2.1', description: 'Foundation', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		// First category and detail row
		expect(result[0].classification).toBe('category');
		expect(result[1].classification).toBe('detail');
		expect(result[1].categoryPrefix).toBe('[Site Work]');
		expect(result[1].finalDescription).toBe('[Site Work]Excavation');

		// Empty row
		expect(result[2].classification).toBe('ignore');

		// Second category and detail row (should not have Site Work prefix)
		expect(result[3].classification).toBe('category');
		expect(result[4].classification).toBe('detail');
		expect(result[4].categoryPrefix).toBe('[Concrete Work]');
		expect(result[4].finalDescription).toBe('[Concrete Work]Foundation');
	});

	it('should handle nested categories with proper empty row management', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Phase 1' },
			{ originalIndex: 1, description: 'Site Work' },
			{ originalIndex: 2, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 3 }, // Empty row - should pop 'Site Work'
			{ originalIndex: 4, description: 'Concrete Work' },
			{ originalIndex: 5, code: '1.2', description: 'Foundation', quantity: 50 },
			{ originalIndex: 6 }, // Empty row - should pop 'Concrete Work'
			{ originalIndex: 7 }, // Empty row - should pop 'Phase 1'
			{ originalIndex: 8, description: 'Phase 2' },
			{ originalIndex: 9, code: '2.1', description: 'Steel Work', quantity: 25 },
		];

		const result = applyCategoriesToRows(rows);

		// Phase 1 > Site Work > Excavation
		expect(result[2].categoryPrefix).toBe('[Phase 1][Site Work]');
		expect(result[2].finalDescription).toBe('[Phase 1][Site Work]Excavation');

		// Phase 1 > Concrete Work > Foundation (Site Work popped)
		expect(result[5].categoryPrefix).toBe('[Phase 1][Concrete Work]');
		expect(result[5].finalDescription).toBe('[Phase 1][Concrete Work]Foundation');

		// Phase 2 > Steel Work (all previous categories popped)
		expect(result[9].categoryPrefix).toBe('[Phase 2]');
		expect(result[9].finalDescription).toBe('[Phase 2]Steel Work');
	});

	// New tests for corrected category stack logic
	it('should NOT drop category when followed by blank rows (no detail rows)', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Category A' },
			{ originalIndex: 1 }, // Blank row after category
			{ originalIndex: 2, description: 'Category B' },
			{ originalIndex: 3, code: '1.1', description: 'Detail Item', quantity: 100 },
		];

		const result = applyCategoriesToRows(rows);

		// Category A should still be in stack when Category B is added
		expect(result[3].categoryPrefix).toBe('[Category A][Category B]');
		expect(result[3].finalDescription).toBe('[Category A][Category B]Detail Item');
	});

	it('should drop category when followed by detail rows and then blank row', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Category A' },
			{ originalIndex: 1, code: '1.1', description: 'Detail Item A', quantity: 100 },
			{ originalIndex: 2 }, // Blank row - should drop Category A
			{ originalIndex: 3, description: 'Category B' },
			{ originalIndex: 4, code: '2.1', description: 'Detail Item B', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		// Category A should be dropped before Category B
		expect(result[4].categoryPrefix).toBe('[Category B]');
		expect(result[4].finalDescription).toBe('[Category B]Detail Item B');
	});

	it('should handle complex scenario with mixed blank rows and detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Phase 1' },
			{ originalIndex: 1 }, // Blank after Phase 1
			{ originalIndex: 2, description: 'Category A' },
			{ originalIndex: 3, code: '1.1', description: 'Detail A1', quantity: 100 },
			{ originalIndex: 4 }, // Blank - should drop Category A
			{ originalIndex: 5, description: 'Category B' },
			{ originalIndex: 6 }, // Blank after Category B
			{ originalIndex: 7, code: '2.1', description: 'Detail B1', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		// Detail A1 should have both Phase 1 and Category A
		expect(result[3].categoryPrefix).toBe('[Phase 1][Category A]');
		expect(result[3].finalDescription).toBe('[Phase 1][Category A]Detail A1');

		// Detail B1 should have Phase 1 and Category B (Category A dropped)
		expect(result[7].categoryPrefix).toBe('[Phase 1][Category B]');
		expect(result[7].finalDescription).toBe('[Phase 1][Category B]Detail B1');
	});

	it('should drop categories with blank rows when equal blank rows follow detail rows', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Category A' },
			{ originalIndex: 1 }, // 1 blank after Category A
			{ originalIndex: 2, code: '1.1', description: 'Detail Item', quantity: 100 },
			{ originalIndex: 3 }, // 1 blank after detail - should drop Category A
			{ originalIndex: 4, description: 'Category B' },
			{ originalIndex: 5, code: '2.1', description: 'Detail B', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		// Detail Item should have Category A
		expect(result[2].categoryPrefix).toBe('[Category A]');
		expect(result[2].finalDescription).toBe('[Category A]Detail Item');

		// Detail B should only have Category B (Category A dropped)
		expect(result[5].categoryPrefix).toBe('[Category B]');
		expect(result[5].finalDescription).toBe('[Category B]Detail B');
	});

	it('should drop categories when new category with blank rows appears', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Category A' },
			{ originalIndex: 1 }, // Blank after Category A
			{ originalIndex: 2, description: 'Category B' },
			{ originalIndex: 3 }, // Blank after Category B - should trigger drop of Category A
			{ originalIndex: 4, code: '1.1', description: 'Detail Item', quantity: 100 },
		];

		const result = applyCategoriesToRows(rows);

		// Detail Item should only have Category B (Category A should be dropped)
		expect(result[4].categoryPrefix).toBe('[Category B]');
		expect(result[4].finalDescription).toBe('[Category B]Detail Item');
	});

	it('should handle comprehensive scenario with corrected rules', () => {
		const rows: ProcessedRow[] = [
			{ originalIndex: 0, description: 'Phase 1' },
			{ originalIndex: 1 }, // Blank after Phase 1 (no details follow immediately)
			{ originalIndex: 2, description: 'Site Work' },
			{ originalIndex: 3, code: '1.1', description: 'Excavation', quantity: 100 },
			{ originalIndex: 4 }, // Blank after detail - should drop Site Work
			{ originalIndex: 5, description: 'Concrete Work' },
			{ originalIndex: 6, code: '2.1', description: 'Foundation', quantity: 75 },
			{ originalIndex: 7 }, // Blank after detail - should drop Concrete Work
			{ originalIndex: 8 }, // Another blank - should drop Phase 1
			{ originalIndex: 9, description: 'Phase 2' },
			{ originalIndex: 10, code: '3.1', description: 'Steel Work', quantity: 50 },
		];

		const result = applyCategoriesToRows(rows);

		// Excavation should have both Phase 1 and Site Work
		expect(result[3].categoryPrefix).toBe('[Phase 1][Site Work]');
		expect(result[3].finalDescription).toBe('[Phase 1][Site Work]Excavation');

		// Foundation should have Phase 1 and Concrete Work (Site Work dropped)
		expect(result[6].categoryPrefix).toBe('[Phase 1][Concrete Work]');
		expect(result[6].finalDescription).toBe('[Phase 1][Concrete Work]Foundation');

		// Steel Work should only have Phase 2 (all previous categories dropped)
		expect(result[10].categoryPrefix).toBe('[Phase 2]');
		expect(result[10].finalDescription).toBe('[Phase 2]Steel Work');
	});
});

describe('validateColumnMapping', () => {
	it('should validate required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			quantity: 2,
			rate: 3,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(true);
		expect(result.errors).toEqual([]);
	});

	it('should detect missing required columns', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 1,
			// missing quantity and rate
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('quantity column is required');
		expect(result.errors).toContain('rate column is required');
	});

	it('should detect duplicate mappings', () => {
		const mapping: ColumnMapping = {
			code: 0,
			description: 0, // duplicate index
			quantity: 1,
			rate: 2,
		};

		const result = validateColumnMapping(mapping);
		expect(result.isValid).toBe(false);
		expect(result.errors).toContain('Column index 0 is mapped to multiple fields');
	});
});

describe('transformToImportData', () => {
	it('should transform classified rows to import format', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				classification: 'category' as const,
				description: 'Site Work',
				finalDescription: 'Site Work',
			},
			{
				originalIndex: 1,
				classification: 'detail' as const,
				code: '1.1',
				description: 'Excavation',
				finalDescription: '[Site Work]Excavation',
				quantity: 100,
				uom: 'm3',
				rate: 50,
				factor: 1.1,
			},
			{
				originalIndex: 2,
				classification: 'ignore' as const,
			},
		];

		const result = transformToImportData(classifiedRows, 'project-123');

		expect(result.project_id).toBe('project-123');
		expect(result.items).toHaveLength(1);
		expect(result.items[0]).toEqual({
			code: '1.1',
			description: '[Site Work]Excavation',
			quantity: 100,
			unit: 'm3',
			material_rate: 50,
			factor: 1.1,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			remarks: undefined,
		});
	});

	it('should handle missing values gracefully', () => {
		const classifiedRows = [
			{
				originalIndex: 0,
				classification: 'detail' as const,
				code: '1.1',
				description: 'Test item',
				finalDescription: 'Test item',
				// missing quantity, rate, etc.
			},
		];

		const result = transformToImportData(classifiedRows, 'project-123');

		expect(result.items[0]).toEqual({
			code: '1.1',
			description: 'Test item',
			quantity: 0,
			unit: '',
			material_rate: 0,
			factor: undefined,
			labor_rate: undefined,
			productivity_per_hour: undefined,
			remarks: undefined,
		});
	});
});
